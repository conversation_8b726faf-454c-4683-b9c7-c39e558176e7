package com.reagent.order.rpc.orderextra;

import com.reagent.order.base.order.dto.AppendListExtraValueRequest;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.ListExtraValueRequest;
import com.reagent.order.base.order.dto.ListOrderExtraDTO;
import com.reagent.order.base.order.mapper.OrderExtraMapper;
import com.reagent.order.base.order.model.OrderExtraDO;
import com.reagent.order.base.order.service.OrderExtraRpcService;
import com.reagent.order.base.order.translator.OrderExtraTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2021/6/28 16:00
 */
@MSharpService
public class OrderExtraRpcServiceImpl implements OrderExtraRpcService {

    @Resource
    private OrderExtraMapper orderExtraMapper;

    /**
     * 批量插入列表
     *
     * @param baseOrderExtraDTOList
     * @return
     */
    @Override
    public RemoteResponse<Integer> insertList(List<BaseOrderExtraDTO> baseOrderExtraDTOList) {
        Preconditions.notEmpty(baseOrderExtraDTOList, "批量插入订单扩展表入参不可为空");
        List<OrderExtraDO> orderExtraDOList = OrderExtraTranslator.dtoListToDoList(baseOrderExtraDTOList);
        int successCount = orderExtraMapper.insertList(orderExtraDOList);
        Preconditions.isTrue(successCount > 0, "插入订单扩展表条目失败，orderExtraDTOList="+ JsonUtils.toJson(baseOrderExtraDTOList));
        return RemoteResponse.<Integer>custom().setSuccess().setData(successCount);
    }

    @Override
    public RemoteResponse<Boolean> saveList(List<BaseOrderExtraDTO> baseOrderExtraDTOList) {
        if(CollectionUtils.isEmpty(baseOrderExtraDTOList)){
            return RemoteResponse.success();
        }
        List<OrderExtraDO> orderExtraDOList = OrderExtraTranslator.dtoListToDoList(baseOrderExtraDTOList);
        Set<Integer> extraKeySet = orderExtraDOList.stream().map(OrderExtraDO::getExtraKey).filter(Objects::nonNull).collect(Collectors.toSet());
        for(Integer extraKey : extraKeySet){
            List<OrderExtraDO> extraDOList = orderExtraDOList.stream().filter(orderExtraDO -> extraKey.equals(orderExtraDO.getExtraKey()) && orderExtraDO.getOrderId() != null).collect(Collectors.toList());
            List<Integer> orderIdList = extraDOList.stream().map(OrderExtraDO::getOrderId).collect(Collectors.toList());
            List<OrderExtraDO> existExtraList = orderExtraMapper.selectByOrderIdInAndExtraKey(orderIdList, extraKey);

            List<Integer> existOrderIdList = existExtraList.stream().map(OrderExtraDO::getOrderId).collect(Collectors.toList());
            List<OrderExtraDO> toInsertExtraList = extraDOList.stream()
                    .filter(orderExtraDO -> !existOrderIdList.contains(orderExtraDO.getOrderId()))
                    .collect(Collectors.toList());
            List<OrderExtraDO> toUpdateExtraList = extraDOList.stream()
                    .filter(orderExtraDO -> existOrderIdList.contains(orderExtraDO.getOrderId()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(toInsertExtraList)){
                orderExtraMapper.insertList(toInsertExtraList);
            }
            if(CollectionUtils.isNotEmpty(toUpdateExtraList)){
                orderExtraMapper.batchUpdateExtraValue(toUpdateExtraList);
            }
        }
        return RemoteResponse.success();
    }

    /**
     * 通过订单id集合选择条目
     *
     * @param orderIdCollection
     * @return
     */
    @Override
    public RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderIdList(Collection<Integer> orderIdCollection) {
        Preconditions.notEmpty(orderIdCollection, "通过订单id集合选择条目入参不可为空");
        List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderIdIn(orderIdCollection);
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = OrderExtraTranslator.doListToDtoList(orderExtraDOList);
        return RemoteResponse.<List<BaseOrderExtraDTO>>custom().setData(baseOrderExtraDTOList).setSuccess();
    }

    /**
     * 通过订单号集合选择条目
     *
     * @param orderNoCollection
     * @return
     */
    @Override
    public RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderNoList(Collection<String> orderNoCollection) {
        Preconditions.notEmpty(orderNoCollection, "通过订单号集合选择条目入参不可为空");
        List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderNoIn(orderNoCollection);
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = OrderExtraTranslator.doListToDtoList(orderExtraDOList);
        return RemoteResponse.<List<BaseOrderExtraDTO>>custom().setData(baseOrderExtraDTOList).setSuccess();
    }

    /**
     * 通过订单id和操作类型id（extra value）查询条目
     *
     * @param query
     * @return
     */
    @Override
    public RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderIdAndExtraKey(BaseOrderExtraDTO query) {
        Preconditions.isTrue(query != null && query.getOrderId() != null, "通过订单id和额外操作id选择条目的订单id入参不可为空");
        List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderIdAndExtraKey(query.getOrderId(), query.getExtraKey());
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = OrderExtraTranslator.doListToDtoList(orderExtraDOList);
        return RemoteResponse.<List<BaseOrderExtraDTO>>custom().setData(baseOrderExtraDTOList).setSuccess();
    }

    /**
     * 通过订单id列表和操作类型id（extra value）查询条目
     * @param orderIdList
     * @param extraKey
     * @return
     */
    @Override
    public RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderIdInAndExtraKey(Collection<Integer> orderIdList, Integer extraKey) {
        Preconditions.notEmpty(orderIdList, "通过订单id和额外操作id选择条目的订单id入参不可为空");
        List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderIdInAndExtraKey(orderIdList, extraKey);
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = OrderExtraTranslator.doListToDtoList(orderExtraDOList);
        return RemoteResponse.<List<BaseOrderExtraDTO>>custom().setData(baseOrderExtraDTOList).setSuccess();
    }

    @Override
    public RemoteResponse<Boolean> deleteInOrderId(Collection<Integer> orderIdCol) {
        Preconditions.isTrue(orderIdCol.size() <= 200, "订单id上限为200个");
        orderExtraMapper.deleteInOrderId(orderIdCol);
        return RemoteResponse.success();
    }

    @Override
    public synchronized RemoteResponse<Boolean> appendToListExtraValue(AppendListExtraValueRequest request) {
        Preconditions.notNull(request, "请求参数不能为空");
        Preconditions.notNull(request.getOrderId(), "订单ID不能为空");
        Preconditions.notNull(request.getExtraKey(), "扩展键不能为空");
        Preconditions.notEmpty(request.getAppendValues(), "追加的值列表不能为空");

        Integer orderId = request.getOrderId();
        Integer extraKey = request.getExtraKey();
        List<String> appendValues = request.getAppendValues();

        // 校验extraKey必须为List类型
        OrderExtraEnum orderExtraEnum = validateListTypeExtraKey(extraKey);

        // 查询已有记录
        List<OrderExtraDO> existingList = orderExtraMapper.selectByOrderIdAndExtraKey(orderId, extraKey);
        OrderExtraDO existingRecord = existingList.isEmpty() ? null : existingList.get(0);

        String existingValue = Objects.nonNull(existingRecord) ? existingRecord.getExtraValue() : null;
        List<String> currentList = parseJsonList(existingValue);

        // 去重并保持顺序
        Set<String> mergedSet = new LinkedHashSet<>(currentList);
        mergedSet.addAll(appendValues);

        String newJsonValue = JsonUtils.toJson(New.list(mergedSet));

        // 更新或插入
        if (Objects.nonNull(existingRecord)) {
            existingRecord.setExtraValue(newJsonValue);
            orderExtraMapper.batchUpdateExtraValue(Collections.singletonList(existingRecord));
        } else {
            OrderExtraDO newRecord = new OrderExtraDO();
            newRecord.setOrderId(orderId);
            newRecord.setExtraKey(extraKey);
            newRecord.setExtraKeyDesc(orderExtraEnum.getDesc());
            newRecord.setExtraValue(newJsonValue);
            orderExtraMapper.insertList(Collections.singletonList(newRecord));
        }

        return RemoteResponse.success();
    }

    @Override
    public RemoteResponse<List<ListOrderExtraDTO>> getListExtraValue(ListExtraValueRequest request) {
        Preconditions.notNull(request, "请求参数不能为空");
        Preconditions.notEmpty(request.getOrderIds(), "订单ID列表不能为空");
        Preconditions.notEmpty(request.getExtraKeys(), "扩展键列表不能为空");

        // 校验所有extraKey必须为List类型
        validateListTypeExtraKeys(request.getExtraKeys());

        List<Integer> orderIds = request.getOrderIds();

        List<OrderExtraDO> extraDOList = orderExtraMapper.selectByOrderIdInAndExtraKeyIn(orderIds, request.getExtraKeys());
        if (CollectionUtils.isEmpty(extraDOList)) {
            return RemoteResponse.success(New.emptyList());
        }

        List<ListOrderExtraDTO> resultList = extraDOList.stream().map(record -> {
            ListOrderExtraDTO item = new ListOrderExtraDTO();
            item.setOrderId(record.getOrderId());
            item.setExtraKey(record.getExtraKey());
            item.setExtraValueList(parseJsonList(record.getExtraValue()));
            return item;
        }).collect(Collectors.toList());

        return RemoteResponse.success(resultList);
    }

    /**
     * 解析 JSON 成 List<String>
     */
    private List<String> parseJsonList(String json) {
        if (StringUtils.isBlank(json)) {
            return New.emptyList();
        }
        List<String> list = JsonUtils.parseList(json, String.class);
        return Objects.nonNull(list) ? list : New.emptyList();
    }

    /**
     * 校验extraKey必须为List类型
     */
    private OrderExtraEnum validateListTypeExtraKey(Integer extraKey) {
        OrderExtraEnum orderExtraEnum = OrderExtraEnum.getByValue(extraKey);
        Preconditions.notNull(orderExtraEnum, "不支持的扩展键：" + extraKey);
        Preconditions.isTrue(java.util.List.class.equals(orderExtraEnum.getDataTypeClass()),
                "扩展键[" + extraKey + "]不是List类型，仅支持List类型的extraKey");
        return orderExtraEnum;
    }

    /**
     * 批量校验extraKey必须为List类型
     */
    private void validateListTypeExtraKeys(List<Integer> extraKeys) {
        for (Integer extraKey : extraKeys) {
            validateListTypeExtraKey(extraKey);
        }
    }

}
